.typical-text-button {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;

  i {
    margin-right: 3px;
    position: relative;
    /* top: -1px; */
    font-size: 14px;
    color: var(--g-text-color-2) !important;
  }

  span {
    font-size: 14px;
    color: var(--g-text-color-2) !important;
  }

  &:hover {
    opacity: 1;
  }
}

.typical-search-box {
  height: 36px;
  padding: 8px 14px;
  border-radius: 100px;
  --el-text-color-placeholder: var(--g-block-bg-6);
  background-color: var(--g-block-bg-2);

  .el-input__wrapper {
    background-color: unset !important;
    box-shadow: unset !important;
  }
}

.typical-search-box-2 {
  height: 44px;
  padding: 12px;
  border-radius: 5px;
  background-color: var(--g-block-bg-2);

  .el-input__wrapper {
    background-color: unset !important;
    box-shadow: unset !important;
  }
}

.el-dialog {
  &.typical-dialog {
    padding: 20px;
  }
}

.typical-form {
  .el-form-item {
    display: block;
    .el-form-item__label {
      opacity: 0.7;
    }
    .el-input__inner {
      text-align: left !important;
      &::placeholder {
        color: var(--g-white);
        opacity: 0.7;
      }
    }
    .el-form-item__content {
      line-height: 38px;
    }
    .el-input,
    .el-select__wrapper {
      height: 44px !important;
      line-height: 44px !important;
    }
    .el-date-editor {
      width: 100%;
    }
  }
}

.typical-radio-group {
  padding: 0 10px;
  border-radius: 4px;
  height: 44px !important;
  line-height: 44px !important;
  background-color: var(--g-block-bg-10);
}

.typical-tabs {
  .el-tabs__nav-wrap {
    &::after {
      background-color: var(--g-block-bg-2);
    }
  }
  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    flex-grow: 1;
    flex-shrink: 1;
    height: 57px !important;
    padding: 0 !important;
    border-right: unset !important;
    background-color: transparent !important;
  }

  .el-tabs__active-bar {
    height: 5px;
    background-color: var(--g-block-bg-11);
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
  }
}

.typical-dialog-footer {
  .el-button {
    border-radius: 6px;
  }
}

.custom-confirm {
  background-color: var(--g-bg) !important;
  padding: 24px !important;
  .el-dialog__header {
    padding-bottom: 0 !important;
  }
}

.typical-edit-table {
  .el-input {
    .el-input__wrapper {
      background-color: transparent;
      box-shadow: unset;
      border: unset;
    }
  }
}

.hopc-6 {
  opacity: 0.6;
  &:hover {
    opacity: 1;
  }
}

.hopc-7 {
  opacity: 0.7;
  &:hover {
    opacity: 1;
  }
}

.hopc-8 {
  opacity: 0.8;
  &:hover {
    opacity: 1;
  }
}

.hopc-9 {
  opacity: 0.9;
  &:hover {
    opacity: 1;
  }
}
