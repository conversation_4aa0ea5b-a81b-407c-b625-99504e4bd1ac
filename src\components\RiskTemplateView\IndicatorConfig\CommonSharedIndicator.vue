<script setup lang="ts">
import { deepClone, isNone } from '@/script';
import { computed, defineAsyncComponent, reactive, useTemplateRef, watch } from 'vue';
import ApplyAssetScope from './ApplyAssetScope.vue';
import { Repos, type RiskRule } from '../../../../../xtrade-sdk/dist';
import { ElMessage } from 'element-plus';
import { RiskCheckTime, RiskCheckTimes, RiskStepControl, RiskStepControls } from '@/enum/riskc';

const Indicators = {
  blackList: { label: '黑名单', value: 1 },
};

const componentsMap: Record<number, unknown> = {
  [Indicators.blackList.value]: defineAsyncComponent(
    () => import('./RuleTemplate/BlackAssetKind.vue'),
  ),
};

const components = Object.values(componentsMap);

const { contextRule } = defineProps<{ contextRule: RiskRule }>();
const componentName = computed(() => componentsMap[contextRule.templateId] || components[0]);
const formName = reactive({ name: '' });
const formData = reactive({
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  stepControl: {
    target: RiskStepControl.instruction_and_order.value,
    frequency: 0,
    time: RiskCheckTime.before.value,
  },
});

const $form = useTemplateRef('$form');
const $formName = useTemplateRef('$formName');
const $riskParam = useTemplateRef('$riskParam');
const $scope = useTemplateRef('$scope');

const rulesName = {
  name: [{ required: true, message: '请输入指标名字', trigger: 'blur' }],
};

const rules = {
  'stepControl.target': [{ required: true, message: '请选择风控环节', trigger: 'blur' }],
  'stepControl.time': [{ required: true, message: '请选择时机', trigger: 'blur' }],
  'stepControl.frequency': [{ required: true, message: '请指定风控频率', trigger: 'blur' }],
  startDate: [{ required: true, message: '请输入开始日期', trigger: 'blur' }],
  endDate: [
    { required: true, message: '请输入结束日期', trigger: 'blur' },
    { validator: checkEndDate, trigger: 'blur' },
  ],
  startTime: [{ required: false, message: '请输入开始时间', trigger: 'blur' }],
  endTime: [
    { required: false, message: '请输入结束时间', trigger: 'blur' },
    { validator: checkEndTime, trigger: 'blur' },
  ],
};

watch(() => contextRule, handleConextChange, { immediate: true });

const is4Creation = computed(() => {
  return !contextRule || isNone(contextRule.id);
});

const scopeConfig = computed(() => {
  const { kindCodes, baseConditions } = contextRule.configuration;
  return { kindCodes, overlaies: baseConditions.map(x => x.name) };
});

const frequencyDesc = computed(() => {
  const { target, frequency } = formData.stepControl;
  if (frequency == 0) {
    return target == RiskStepControl.order.value ? '(0s表示未到柜台)' : '(0s表示事前)';
  }

  return '';
});

function handleConextChange() {
  resetFormData();
}
function resetFormData() {
  const { ruleName, checkInterval, checkTime, checkObject } = contextRule;
  const { beginDay, beginTime, endDay, endTime } = contextRule;
  const fd = formData;
  formName.name = ruleName;

  fd.stepControl.target = checkObject;
  fd.stepControl.time = checkTime;
  fd.stepControl.frequency = checkInterval;
  fd.startDate = beginDay.toString();
  fd.endDate = endDay.toString();
  let t_start = beginTime.toString();
  let t_end = endTime.toString();

  while (t_start.length < 6) {
    t_start = '0' + t_start;
  }

  while (t_end.length < 6) {
    t_end = '0' + t_end;
  }

  fd.startTime = t_start;
  fd.endTime = t_end;
}

function checkEndDate(rule: any, value: string, callback: (error?: Error) => void) {
  const { startDate, endDate } = formData;
  if (startDate && endDate && endDate < startDate) {
    callback(new Error('结束日期，大于开始日期'));
  } else if (!endDate) {
    callback(new Error('请输入结束日期'));
  } else {
    callback();
  }
}

function checkEndTime(rule: any, value: string, callback: (error?: Error) => void) {
  const { startTime, endTime } = formData;
  if (startTime && endTime && endTime <= startTime) {
    callback(new Error('结束时间，大于等于开始时间'));
  } else {
    callback();
  }
}

const handleValidate = async () => {
  let r1 = true,
    r2 = true,
    r3 = true,
    r4 = true;
  try {
    await $formName.value!.validate();
  } catch (ex) {
    console.error(ex);
    r1 = false;
  }
  try {
    await $form.value!.validate();
  } catch (ex) {
    console.error(ex);
    r2 = false;
  }
  try {
    await ($riskParam.value! as any).validate();
  } catch (ex) {
    console.error(ex);
    r3 = false;
  }
  try {
    await $scope.value!.validate();
  } catch (ex) {
    console.error(ex);
    r4 = false;
  }

  if (r1 && r2 && r3 && r4) {
    handleSave();
  }
};

const repoInstance = new Repos.RiskControlRepo();
const emitter = defineEmits<{
  saved: [];
}>();

const handleSave = async () => {
  // 资产分类范围
  const scopeSetting = $scope.value!.getSetting();
  const { kindCodes, overlaies } = scopeSetting;
  // 风控规则详细配置
  const riskParam = ($riskParam.value! as any).getSetting();

  // 创建规则副本用于创建
  const cloned = deepClone(contextRule);
  // 更新规则名称
  cloned.ruleName = formName.name;
  // 更新其他附属字段
  const { startDate, endDate, startTime, endTime, stepControl } = formData;
  cloned.beginDay = Number(startDate);
  cloned.endDay = Number(endDate);
  cloned.beginTime = Number(startTime);
  cloned.endTime = Number(endTime);
  cloned.checkObject = stepControl.target;
  cloned.checkTime = stepControl.time;
  cloned.checkInterval = stepControl.frequency;

  // 组合规则的参数配置
  cloned.configuration = {
    kindCodes,
    baseConditions: overlaies.map(x => ({
      name: x.variable,
      expression: x.expression,
      value: x.value,
    })),
    riskParam,
  };

  console.log(cloned);
  const resp = is4Creation.value
    ? await repoInstance.CreateRule(cloned)
    : await repoInstance.UpdateRule(cloned);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    ElMessage.success('保存成功');
    emitter('saved');
  } else {
    ElMessage.error(`保存失败：${errorCode}/${errorMsg}`);
  }
};
</script>

<template>
  <div class="view-idc-panel" h-full pt-5 p-x-12 flex flex-col gap-10>
    <div h-40>
      <el-form ref="$formName" :model="formName" :rules="rulesName" label-width="80px">
        <el-form-item label="指标名称" prop="name">
          <div class="name-row" w-full flex jcsb aic>
            <el-input v-model.trim="formName.name" placeholder="请输入指标名称" clearable />
            <el-button type="primary" @click="handleValidate" ml-10>
              {{ is4Creation ? '创建' : '更新' }}
            </el-button>
            <!-- <el-button>新增</el-button> -->
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div pt-10 flex-1 flex of-hidden>
      <div h-full w-530 of-y-auto>
        <el-form ref="$form" :model="formData" :rules="rules" label-width="80px">
          <KeepAlive>
            <component
              ref="$riskParam"
              :is="componentName"
              v-bind:ruleSetting="contextRule.configuration.riskParam"
            ></component>
          </KeepAlive>
          <div class="custom-row" flex aic gap-10>
            <div w-270>
              <el-form-item label="控制环节" prop="stepControl.target">
                <el-select v-model="formData.stepControl.target">
                  <el-option
                    v-for="(item, idx) in RiskStepControls"
                    :key="idx"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <!--
            <div class="ctr-check-time" w-135>
              <el-form-item label="时机" prop="stepControl.time">
                <el-select v-model="formData.stepControl.time">
                  <el-option
                    v-for="(item, idx) in RiskCheckTimes"
                    :key="idx"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="post-item">
              <el-input-number
                v-model="formData.stepControl.frequency"
                :controls="false"
                :precision="0"
                :min="0"
                :max="999"
                placeholder="间隔时长"
                style="width: 90px"
                clearable
              />
            </div>
            <label class="placed-label">秒执行一次{{ frequencyDesc }}</label>
            -->
          </div>
          <div class="custom-row" flex aic gap-16>
            <div w-270>
              <el-form-item label="生效日期" prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  type="date"
                  value-format="YYYYMMDD"
                  placeholder="开始日期"
                  clearable
                />
              </el-form-item>
            </div>
            <label class="placed-label">至</label>
            <div w-190 class="post-item">
              <el-form-item label="" prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  value-format="YYYYMMDD"
                  placeholder="结束日期"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
          <div class="custom-row" flex aic gap-16>
            <div w-270>
              <el-form-item label="生效时间" prop="startTime">
                <el-time-picker
                  v-model="formData.startTime"
                  value-format="HHmmss"
                  placeholder="开始时间"
                  clearable
                />
              </el-form-item>
            </div>
            <label class="placed-label">至</label>
            <div w-190 class="post-item">
              <el-form-item label="" prop="endTime">
                <el-time-picker
                  v-model="formData.endTime"
                  value-format="HHmmss"
                  placeholder="结束时间"
                  clearable
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div h-full flex-1 of-hidden>
        <ApplyAssetScope ref="$scope" :scope="scopeConfig"></ApplyAssetScope>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-idc-panel {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .name-row {
    :deep() {
      > .el-input {
        width: 100px;
        flex-grow: 1;
        flex-shrink: 1;
      }
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .ctr-check-time {
    :deep() {
      .el-form-item__label {
        width: 55px !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>

<style>
.view-idc-panel {
  .el-form-item__label {
    height: 40px;
    line-height: 40px;
  }

  .el-select,
  .el-select__wrapper,
  .el-input {
    height: 40px;
  }
}
</style>
