<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { TableV2SortOrder, ElSwitch, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { deepClone, formatDateTime, getUser, isJson, isNone, isNotJson, remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';
import { RiskCheckTime, RiskCheckTimes, RiskStepControl, RiskStepControls } from '@/enum/riskc';

import {
  Repos,
  type RiskIndicator,
  type RiskRule,
  type RiskRuleConfiguration,
  type RiskTemplate,
} from '../../../../xtrade-sdk/dist';

interface RiskRuleRowData extends RiskRule {
  localId: number | string;
}

interface CellRenderParam {
  rowData: RiskRuleRowData;
  cellData: any;
}

// 基础列定义
const columns: ColumnDefinition<RiskRuleRowData> = [
  {
    key: 'ruleName',
    title: '风控规则名称',
    width: 300,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      const { id, ruleName } = params.rowData;
      if (isNone(id)) {
        return (
          <span>
            <span class="c-red">(待保存)</span> {ruleName}
          </span>
        );
      } else {
        return <span>{ruleName}</span>;
      }
    },
  },
  {
    key: 'active',
    title: '是否启用',
    width: 100,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      const { active } = params.rowData;
      return <ElSwitch v-model={active}>{active ? '是' : '否'}</ElSwitch>;
    },
  },
  {
    key: 'beginTime',
    title: '运行时间',
    width: 300,
    sortable: true,
    cellRenderer: formatRunningTime,
  },
  {
    key: 'checkObject',
    title: '检查对象',
    width: 100,
    sortable: true,
    cellRenderer: formatCheckObject,
  },
  {
    key: 'checkTime',
    title: '检查时机',
    width: 100,
    sortable: true,
    cellRenderer: formatCheckTime,
  },
  {
    key: 'checkInterval',
    title: '间隔检查(秒)',
    width: 110,
    sortable: true,
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTimeProxy,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    sortable: true,
    cellRenderer: formatDateTimeProxy,
  },
  // {
  //   key: 'createUserId',
  //   title: '创建者用户ID',
  //   width: 120,
  //   sortable: true,
  // },
  // {
  //   key: 'orgId',
  //   title: '机构ID',
  //   width: 100,
  //   sortable: true,
  // },
];

// 行操作
const rowActions: RowAction<RiskRuleRowData>[] = [
  {
    label: '删除',
    icon: 'remove',
    type: 'text',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const { contextTemplate } = defineProps<{ contextTemplate: RiskTemplate | null }>();
const repoInstance = new Repos.RiskControlRepo();

async function deleteRow(row: RiskRuleRowData) {
  const result = await deleteConfirm('删除风控项', `确认删除此风控项： ${row.ruleName}？`);

  if (result !== true) {
    return;
  }

  const resp = await repoInstance.DeleteRule(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    remove(records.value, x => x.id == row.id);
    unselectRow();
  } else {
    ElMessage.error('风控规则删除失败：' + errorMsg);
  }
}

const user = getUser()!;
const emitter = defineEmits<{
  add: [callback: (tmpl: RiskTemplate, current: RiskIndicator) => void];
  select: [rule: RiskRule | null];
}>();

function request2Add() {
  emitter('add', addCallback);
}

function assignRuleConfig() {
  const blackListConfig: RiskRuleConfiguration = {
    kindCodes: [],
    baseConditions: [],
    riskParam: null,
  };
  return blackListConfig;
}

function addCallback(tmpl: RiskTemplate, idc: RiskIndicator) {
  const date = new Date();
  const now = date.getTime();
  const today_num = Number(formatDateTime(now, 'yyyyMMdd'));
  const rname = `${idc.indicatorName}_${formatDateTime(date, 'yyMMdd')}_${formatDateTime(date, 'hhmmss')}`;
  const rule: RiskRuleRowData = {
    id: null as any,
    localId: now,
    templateId: tmpl.id,
    ruleName: rname,
    indicatorId: idc.id,
    configuration: assignRuleConfig(),
    beginTime: 93000,
    endTime: 150000,
    beginDay: today_num,
    endDay: today_num,
    checkObject: RiskStepControl.instruction.value,
    checkTime: RiskCheckTime.before.value,
    checkInterval: 60,
    active: true,
    createUserId: user.userId,
    orgId: user.orgId,
    createTime: now,
    updateTime: now,
  };

  records.value.push(rule);
  nextTick(() => {
    selectRow(rule);
  });
}

function selectRow(rule: number | string | RiskRule | undefined) {
  const matched = isJson(rule) ? rule : records.value.find(r => r.id == rule);
  emitter('select', (matched as any) || null);
}

function unselectRow() {
  emitter('select', null);
}

function handleClick(row: RiskRuleRowData) {
  emitter('select', deepClone(row));
}

const records = ref<RiskRuleRowData[]>([]);
const recordsByTmpl = computed(() => {
  const all = records.value;
  return contextTemplate ? all.filter(x => x.templateId == contextTemplate.id) : all;
});

watch(() => recordsByTmpl.value, handleRecordsChange);

function handleRecordsChange() {
  handleClick(recordsByTmpl.value[0] || null);
}

function formatDateTimeProxy(params: { cellData: any }) {
  return <span>{formatDateTime(params.cellData, 'yyyy-MM-dd hh:mm:ss')}</span>;
}

function formatRunningTime(params: { rowData: RiskRuleRowData }) {
  function format(date: number | string, hms: number | string) {
    if (!date || !hms) {
      return 'NA';
    }

    date = date.toString();
    hms = hms.toString();

    while (hms.length < 6) {
      hms = '0' + hms;
    }

    const date_str = formatDateTime(date, 'yyyy-MM-dd');
    const hms_str = hms.substring(0, 2) + ':' + hms.substring(2, 4) + ':' + hms.substring(4, 6);
    return date_str + ' ' + hms_str;
  }

  const { beginDay, beginTime, endDay, endTime } = params.rowData;
  return (
    <span>
      {format(beginDay, beginTime)} ~ {format(endDay, endTime)}
    </span>
  );
}

function formatCheckObject(params: CellRenderParam) {
  const { checkObject } = params.rowData;
  const matched = RiskStepControls.find(x => x.value == checkObject);
  return <span>{matched?.label || checkObject}</span>;
}

function formatCheckTime(params: CellRenderParam) {
  const { checkTime } = params.rowData;
  const matched = RiskCheckTimes.find(x => x.value == checkTime);
  return <span>{matched?.label || checkTime}</span>;
}

async function request() {
  const list = ((await repoInstance.QueryRules()).data || []) as RiskRuleRowData[];
  list.forEach(item => {
    item.localId = item.id;
  });
  list.forEach(item => {
    const cfg = item.configuration;
    if (isNotJson(cfg) || JSON.stringify(cfg) == '{}') {
      item.configuration = assignRuleConfig();
    }
    item.configuration = assignRuleConfig();
  });
  records.value = list;
}

function refresh() {
  request();
}

defineExpose({
  refresh,
});

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    identity="localId"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="recordsByTmpl"
    :row-actions="rowActions"
    :row-action-width="80"
    @row-click="handleClick"
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button type="primary" @click="request2Add">
          <span flex aic gap-6>
            <i class="iconfont icon-add"></i>
            <span>新增</span>
          </span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
