<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import UserDialog from './UserDialog.vue';
import ResetPasswordDialog from './ResetPasswordDialog.vue';
import { onMounted, ref } from 'vue';
import { TableV2SortOrder, ElMessage, ElMessageBox } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { enumToArray, formatDateTime, hasPermission, renderLabel } from '@/script';
import { Repos, UserRole, type MomUser } from '../../../../xtrade-sdk/dist';
import { isUserDisabled, MenuPermitUserManagement, UserStatus } from '@/enum';
import { deleteConfirm } from '@/script/interaction';

const UserRoles = Object.values(UserRole);
const repoInstance = new Repos.AdminRepo();

// 基础列定义
const columns: ColumnDefinition<MomUser> = [
  { key: 'username', title: '用户名', width: 150, sortable: true, fixed: true },
  {
    key: 'roleId',
    title: '角色',
    width: 120,
    sortable: true,
    fixed: true,
    cellRenderer: ({ rowData }: { rowData: MomUser }) => {
      return <span>{renderLabel(rowData.roleId, UserRoles)}</span>;
    },
  },
  { key: 'orgName', title: '所属机构', width: 150, sortable: true },
  {
    key: 'onlineStatus',
    title: '是否在线',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomUser }) => {
      return (
        <span class={rowData.onlineStatus ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]'}>
          {rowData.onlineStatus ? '在线' : '离线'}
        </span>
      );
    },
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomUser }) => {
      return hasPermission(MenuPermitUserManagement.用户状态) ? (
        <el-switch
          modelValue={isUserDisabled(rowData) ? UserStatus.禁止 : UserStatus.正常}
          active-value={UserStatus.正常}
          inactive-value={UserStatus.禁止}
          before-change={() => beforeChange(rowData)}
        />
      ) : (
        <span
          class={
            rowData.status === UserStatus.正常
              ? 'c-[var(--g-green)]'
              : rowData.status === UserStatus.冻结
                ? 'c-[var(--g-orange)]'
                : 'c-[var(--g-red)]'
          }
        >
          {renderLabel(rowData.status, enumToArray(UserStatus))}
        </span>
      );
    },
  },
  { key: 'fullName', title: '姓名', width: 140, sortable: true },
  { key: 'phoneNo', title: '电话号码', width: 120, sortable: true },
  { key: 'qualifications', title: '从业资格', width: 120, sortable: true },
  { key: 'validTime', title: '有效期', width: 150, sortable: true, cellRenderer: formatDate },
  { key: 'email', title: '邮箱地址', width: 200, sortable: true },
  { key: 'creator', title: '创建人', width: 170, sortable: true },
  {
    key: 'createTime',
    title: '创建时间',
    width: 170,
    sortable: true,
    cellRenderer: formatDate,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 170,
    sortable: true,
    cellRenderer: formatDate,
  },
];

// 行操作
const rowActions: RowAction<MomUser>[] = [
  {
    label: '编辑',
    icon: 'edit',
    show: () => hasPermission(MenuPermitUserManagement.编辑),
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '重置密码',
    icon: 'password',
    show: () => hasPermission(MenuPermitUserManagement.重置密码),
    onClick: row => {
      resetPassword(row);
    },
  },
  {
    label: '强制下线',
    icon: 'exit',
    show: () => hasPermission(MenuPermitUserManagement.强制下线),
    disabled: (row: MomUser) => !row.onlineStatus,
    onClick: row => {
      forceLogout(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    show: () => hasPermission(MenuPermitUserManagement.删除),
    onClick: row => {
      deleteRow(row);
    },
  },
];

const records = ref<MomUser[]>([]);

// 对话框状态
const userDialogVisible = ref(false);
const resetPasswordDialogVisible = ref(false);
const currentUser = ref<MomUser>();

// 新建用户
const createUser = () => {
  currentUser.value = undefined;
  userDialogVisible.value = true;
};

// 编辑用户
const editRow = (row: MomUser) => {
  currentUser.value = row;
  userDialogVisible.value = true;
};

// 重置密码
const resetPassword = (row: MomUser) => {
  currentUser.value = row;
  resetPasswordDialogVisible.value = true;
};

// 强制下线
const forceLogout = (row: MomUser) => {
  ElMessageBox.confirm(`确定要强制下线用户 "${row.username}" 吗？`, '下线确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { errorCode, errorMsg } = await AdminService.forceLogoutUser(row.id);
    if (errorCode === 0) {
      ElMessage.success('强制下线成功');
      await request();
    } else {
      ElMessage.error(errorMsg || '强制下线失败');
    }
  });
};

// 删除用户
const deleteRow = async (row: MomUser) => {
  const result = await deleteConfirm('删除用户', `确定要删除用户 "${row.username}" 吗？`);
  if (!result) return;

  const { errorCode, errorMsg } = await AdminService.deleteUser(row.id);
  if (errorCode === 0) {
    ElMessage.success('删除成功');
    await request();
  } else {
    ElMessage.error(errorMsg || '删除失败');
  }
};

function formatDate(params: any) {
  return <span>{formatDateTime(params.cellData)}</span>;
}

async function request() {
  records.value = (await repoInstance.QueryUsers()).data || [];
}

async function beforeChange(row: MomUser) {
  const newStatus = isUserDisabled(row) ? UserStatus.正常 : UserStatus.禁止;
  const { errorCode, errorMsg } = await AdminService.updateUser({
    ...row,
    status: newStatus,
  });
  if (errorCode === 0) {
    row.status = newStatus;
    ElMessage.success('状态修改成功');
    return true;
  } else {
    ElMessage.error(errorMsg || '状态修改失败');
    return false;
  }
}

// 处理对话框成功事件
const handleDialogSuccess = () => {
  request();
};

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="330"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <!-- <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button> -->
        <el-button
          v-if="hasPermission(MenuPermitUserManagement.创建)"
          type="primary"
          @click="createUser"
        >
          <i class="iconfont icon-user-add" mr-5></i>
          <span>新建用户</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 用户编辑对话框 -->
  <UserDialog v-model="userDialogVisible" :user="currentUser" @success="handleDialogSuccess" />

  <!-- 重置密码对话框 -->
  <ResetPasswordDialog
    v-model="resetPasswordDialogVisible"
    :user="currentUser"
    @success="handleDialogSuccess"
  />
</template>

<style scoped></style>
