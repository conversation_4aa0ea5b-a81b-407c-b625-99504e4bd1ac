import { ActionLogTypeEnum } from '../config/core.js';
import { BaseRepo } from '../modules/base-repo.js';
import {
  FormBroker,
  FormMenu,
  FormOrganization,
  FormPermission,
  FormRole,
  FormTerminal,
  FormUser,
  MomBroker,
  MomMenu,
  MomMenuTree,
  MomOrganization,
  MomPermission,
  MomRole,
  MomTerminal,
  MomUser,
  UserPasswordChange,
  UserPasswordReset,
  MomActionLog,
} from '../types/table/admin.js';
import { LegacyFundInfo } from '../types/table/fund.js';

export class AdminRepo extends BaseRepo {
  /**
   * 查询系统用户信息
   */
  async QueryUsers() {
    return await this.assist.Get<MomUser[]>('user');
  }

  /**
   * 创建用户
   */
  async CreateUser(user: FormUser) {
    return await this.assist.Post<MomUser>('/user', {}, user);
  }

  /**
   * 更新用户
   */
  async UpdateUser(user: <PERSON><PERSON><PERSON>) {
    return await this.assist.Put<MomUser>('/user', {}, user);
  }

  /**
   * 删除用户
   */
  async DeleteUser(target_user_id: number) {
    return await this.assist.Delete<[]>('user', { target_user_id });
  }

  /**
   * 重置用户密码
   */
  async ResetUserPassword(params: UserPasswordReset) {
    return await this.assist.Put<[]>('user/password', params);
  }

  /**
   * 强制指定用户登出
   */
  async ForceLogoutUser(op_user_id: number) {
    return await this.assist.Put<[]>('user/logout', { op_user_id });
  }

  /**
   * 用户自己修改密码
   */
  async ChangeUserPassword(params: UserPasswordChange) {
    return await this.assist.Put<[]>('normalUser/password', params);
  }

  /**
   * 查询分享给用户的产品
   */
  async QueryUserProducts(find_user_id: number) {
    return await this.assist.Get<LegacyFundInfo[]>('user/fundList', { find_user_id });
  }

  /**
   * 将产品分享给用户
   */
  async ShareUserProducts(
    find_user_id: number,
    fundInfos: Array<{ id: number; fundType: number; fundName: string }>,
  ) {
    return await this.assist.Post<void>('user/BindFund', { find_user_id }, fundInfos);
  }

  /**
   * 取消分享产品给用户
   */
  async UnshareUserProducts(find_user_id: number, fund_ids: string) {
    return await this.assist.Post<void>('user/cancelBindFund', { find_user_id, fund_ids });
  }

  /**
   * 查询系统机构列表
   */
  async QueryOrgs() {
    return await this.assist.Get<MomOrganization[]>('/organization');
  }

  /**
   * 创建系统机构
   */
  async CreateOrg(org: FormOrganization) {
    return await this.assist.Post<MomOrganization>('/organization', {}, org);
  }

  /**
   * 更新系统机构
   */
  async UpdateOrg(org: MomOrganization) {
    return await this.assist.Put<[]>('/organization', {}, org);
  }

  /**
   * 删除系统机构
   */
  async DeleteOrg(org_id: number) {
    return await this.assist.Delete<[]>('/organization', { org_id });
  }

  /**
   * 查询经纪商列表
   */
  async QueryBrokers() {
    return await this.assist.Get<MomBroker[]>('/broker');
  }

  /**
   * 创建经纪商
   */
  async CreateBroker(broker: FormBroker) {
    return await this.assist.Post<MomBroker>('/broker', {}, broker);
  }

  /**
   * 更新经纪商
   */
  async UpdateBroker(broker: MomBroker) {
    return await this.assist.Put<[]>('/broker', {}, broker);
  }

  /**
   * 删除经纪商
   */
  async DeleteBroker(broker_id: number) {
    return await this.assist.Delete<[]>('/broker', { broker_id });
  }

  /**
   * 查询系统角色列表
   */
  async QueryRoles() {
    return await this.assist.Get<MomRole[]>('/role');
  }

  /**
   * 根据角色ID查询角色详情
   */
  async QueryRoleById(role_id: number) {
    return await this.assist.Get<MomRole>('/role/detail', { role_id });
  }

  /**
   * 创建系统角色
   */
  async CreateRole(role: FormRole) {
    return await this.assist.Post<MomRole>(
      'role',
      {},
      {
        ...role,
        activeFlag: true,
      },
    );
  }

  /**
   * 更新系统角色
   */
  async UpdateRole(role: MomRole) {
    return await this.assist.Put<MomRole>('/role', {}, role);
  }

  /**
   * 删除系统角色
   */
  async DeleteRole(role_id: number) {
    return await this.assist.Delete<void>('/role', { role_id });
  }

  /**
   * 启用/禁用角色
   */
  async ToggleRoleStatus(role_id: number, activeFlag: boolean) {
    return await this.assist.Put<void>('/role/status', {}, { role_id, activeFlag });
  }

  /**
   * 查询角色下的用户列表
   */
  async QueryUsersByRole(role_id: number) {
    return await this.assist.Get<MomUser[]>('/user/roleId', { role_id });
  }

  /**
   * 查询全量菜单列表
   */
  async QueryMenus() {
    return await this.assist.Get<MomMenu[]>('/menu');
  }

  /** 查询全量菜单列表（树形） */
  async QueryMenuTree() {
    return await this.assist.Get<MomMenuTree[]>('/menu/tree');
  }

  /**
   * 创建系统菜单
   */
  async CreateMenu(menu: FormMenu) {
    return await this.assist.Post<MomMenu>('/menu/create', {}, menu);
  }

  /**
   * 更新系统菜单
   */
  async UpdateMenu(menu: MomMenu) {
    return await this.assist.Put<MomMenu>('/menu/edit', {}, menu);
  }

  /**
   * 删除系统菜单
   */
  async DeleteMenu(menu_id: number) {
    return await this.assist.Delete<null>('/menu/delete', { menu_id });
  }

  /**
   * 查询角色菜单
   */
  async QueryRoleMenu(role_id: number) {
    return await this.assist.Get<MomMenuTree[]>('/menu/getRoleMenu', { role_id });
  }

  /**
   * 保存角色菜单权限
   */
  async SaveRoleMenuPermissions(
    role_id: number,
    data: { menuId: number; permissionIds: number[] }[],
  ) {
    return await this.assist.Post<any>(
      'menu/saveRoleMenuAndPermission',
      { role_id },
      data.map(item => {
        return {
          ...item,
          sequence: 0,
        };
      }),
    );
  }

  /**
   * 查询交易终端
   */
  async QueryTerminals() {
    return await this.assist.Get<MomTerminal[]>('/terminal');
  }

  /**
   * 创建交易终端
   */
  async CreateTerminal(terminal: FormTerminal) {
    return await this.assist.Post<MomTerminal>('/terminal', {}, terminal);
  }

  /**
   * 更新交易终端
   */
  async UpdateTerminal(terminal: MomTerminal) {
    return await this.assist.Put<[]>('/terminal', {}, terminal);
  }

  /**
   * 删除交易终端
   */
  async DeleteTerminal(terminal_id: number) {
    return await this.assist.Delete<[]>('/terminal', { terminal_id });
  }

  /**
   * 查询终端账号
   */
  async QueryTerminalAccounts(terminal_id: number) {
    return await this.assist.Get<any[]>('/terminal/accountInfo', { terminal_id });
  }

  /**
   * 查询当前归属的交易日
   */
  async QueryTradingDay() {
    return await this.assist.Get<string>('/tradingDay');
  }

  // ==================== 权限管理相关接口 ====================

  /**
   * 根据菜单ID查询权限列表
   */
  async QueryPermissionsByMenu(menu_id: number) {
    return await this.assist.Get<MomPermission[]>('permission/queryPermissionByMenuId', {
      menu_id,
    });
  }

  /**
   * 创建系统权限
   */
  async CreatePermission(permission: FormPermission) {
    return await this.assist.Post<MomPermission>('permission/addPermission', {}, permission);
  }

  /**
   * 更新系统权限
   */
  async UpdatePermission(permission: MomPermission) {
    return await this.assist.Put<MomPermission>('permission/updatePermission', {}, permission);
  }

  /**
   * 删除系统权限
   */
  async DeletePermission(permission_id: number) {
    return await this.assist.Delete<[]>('permission/deleteByMenuId', { permission_id });
  }

  /**
   * 查询行为日志
   */
  async QueryActionLogs(log_type: ActionLogTypeEnum, op_user_id: number) {
    return await this.assist.Get<MomActionLog[]>('log/action', {
      log_type,
      op_user_id,
      page_no: 1,
      page_size: 1000,
    });
  }
}
